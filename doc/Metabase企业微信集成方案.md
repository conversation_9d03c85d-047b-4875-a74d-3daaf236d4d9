# Metabase企业微信集成方案

## 📋 方案概述

本方案旨在将已部署的Metabase开源版与企业微信进行集成，实现企业微信一键授权登录，提升用户体验和安全性。

## 🎯 集成目标

1. **单点登录(SSO)**: 用户通过企业微信身份直接访问Metabase
2. **权限同步**: 基于企业微信组织架构自动分配Metabase权限
3. **用户管理**: 自动同步企业微信用户信息到Metabase
4. **安全增强**: 利用企业微信的安全机制保护数据访问

## 🏗️ 技术架构

### 整体架构图
```
企业微信 ←→ 中间件服务 ←→ Metabase
    ↓           ↓           ↓
  OAuth2.0   JWT Token   用户认证
  用户信息    权限映射    数据访问
```

### 核心组件
1. **企业微信OAuth2.0服务**: 提供身份认证
2. **中间件认证服务**: 处理认证逻辑和用户映射
3. **Metabase**: 数据分析平台

## 🔧 实现方案

### 方案一：基于反向代理的SSO集成（推荐）

#### 技术栈
- **Nginx**: 反向代理和请求拦截
- **Node.js/Java**: 中间件认证服务
- **Redis**: 会话存储
- **MySQL**: 用户映射存储

#### 实现步骤

##### 1. 企业微信应用配置
```bash
# 企业微信管理后台配置
1. 创建企业应用
2. 获取 CorpID 和 AgentID
3. 配置可信域名
4. 设置授权回调域
```

##### 2. 中间件服务开发
基于现有项目结构，创建认证服务：

```java
// 企业微信OAuth配置
@Configuration
public class WechatWorkConfig {
    @Value("${wechat.work.corpid}")
    private String corpId;
    
    @Value("${wechat.work.corpsecret}")
    private String corpSecret;
    
    @Value("${wechat.work.agentid}")
    private String agentId;
    
    @Value("${wechat.work.redirect.uri}")
    private String redirectUri;
}

// 认证控制器
@RestController
@RequestMapping("/auth/wechat")
public class WechatWorkAuthController {
    
    @GetMapping("/login")
    public ResponseEntity<Object> login() {
        // 构建企业微信授权URL
        String authUrl = buildWechatAuthUrl();
        return ResponseEntity.ok(Map.of("authUrl", authUrl));
    }
    
    @GetMapping("/callback")
    public ResponseEntity<Object> callback(@RequestParam String code) {
        // 1. 通过code获取用户信息
        WechatUserInfo userInfo = getWechatUserInfo(code);
        
        // 2. 创建或更新Metabase用户
        MetabaseUser metabaseUser = createOrUpdateMetabaseUser(userInfo);
        
        // 3. 生成JWT Token
        String token = generateJwtToken(metabaseUser);
        
        // 4. 重定向到Metabase并携带token
        return ResponseEntity.status(302)
            .header("Location", "/metabase?token=" + token)
            .build();
    }
}
```

##### 3. Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 认证服务
    location /auth/ {
        proxy_pass http://localhost:8080/auth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Metabase代理
    location /metabase/ {
        # 认证检查
        auth_request /auth/verify;
        
        proxy_pass http://localhost:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-User-Email $upstream_http_x_user_email;
    }
    
    # 认证验证端点
    location = /auth/verify {
        internal;
        proxy_pass http://localhost:8080/auth/verify;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
    }
}
```

### 方案二：基于Metabase插件的集成

#### 实现步骤

##### 1. 开发Metabase认证插件
```clojure
;; 企业微信认证插件
(ns metabase.integrations.wechat-work
  (:require [metabase.integrations.common :as integrations.common]
            [metabase.models.user :as user]))

(defn wechat-work-auth-url []
  "构建企业微信授权URL")

(defn handle-wechat-callback [code]
  "处理企业微信回调")
```

##### 2. 配置Metabase环境变量
```bash
# 企业微信配置
MB_WECHAT_WORK_ENABLED=true
MB_WECHAT_WORK_CORP_ID=your_corp_id
MB_WECHAT_WORK_CORP_SECRET=your_corp_secret
MB_WECHAT_WORK_AGENT_ID=your_agent_id
```

## 📊 数据库设计

### 用户映射表
```sql
CREATE TABLE wechat_metabase_user_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    wechat_userid VARCHAR(64) NOT NULL UNIQUE,
    wechat_name VARCHAR(100),
    wechat_email VARCHAR(100),
    wechat_department VARCHAR(200),
    metabase_user_id INT,
    metabase_email VARCHAR(100),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_wechat_userid (wechat_userid),
    INDEX idx_metabase_user_id (metabase_user_id)
);
```

### 权限映射表
```sql
CREATE TABLE wechat_metabase_permission_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    wechat_department VARCHAR(200),
    metabase_group_id INT,
    metabase_group_name VARCHAR(100),
    permission_level ENUM('viewer', 'editor', 'admin') DEFAULT 'viewer',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_department (wechat_department)
);
```

## 🔐 安全考虑

### 1. Token安全
- JWT Token设置合理过期时间（建议2小时）
- 使用强密钥签名
- 支持Token刷新机制

### 2. 会话管理
- Redis存储会话信息
- 设置会话超时
- 支持强制登出

### 3. 权限控制
- 基于企业微信部门自动分配权限
- 支持细粒度权限控制
- 定期同步权限变更

## 🚀 部署方案

### 开发环境部署
```bash
# 1. 启动认证服务
cd auth-service
mvn spring-boot:run

# 2. 配置Nginx
sudo nginx -s reload

# 3. 启动Metabase
java -jar metabase.jar
```

### 生产环境部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  auth-service:
    build: ./auth-service
    ports:
      - "8080:8080"
    environment:
      - WECHAT_WORK_CORP_ID=${CORP_ID}
      - WECHAT_WORK_CORP_SECRET=${CORP_SECRET}
    depends_on:
      - redis
      - mysql
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - auth-service
      - metabase
  
  metabase:
    image: metabase/metabase:latest
    ports:
      - "3000:3000"
    environment:
      - MB_DB_TYPE=mysql
      - MB_DB_HOST=mysql
    depends_on:
      - mysql
  
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
  
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=metabase
    ports:
      - "3306:3306"
```

## 📈 监控和日志

### 1. 认证日志
```java
@Slf4j
@Component
public class AuthenticationLogger {
    
    public void logAuthSuccess(String userId, String source) {
        log.info("Authentication success: userId={}, source={}", userId, source);
    }
    
    public void logAuthFailure(String userId, String reason) {
        log.warn("Authentication failed: userId={}, reason={}", userId, reason);
    }
}
```

### 2. 性能监控
- 认证响应时间监控
- 用户登录频率统计
- 错误率监控

## 🔄 用户同步流程

### 自动同步
```java
@Scheduled(fixedRate = 3600000) // 每小时同步一次
public void syncWechatUsers() {
    // 1. 获取企业微信用户列表
    List<WechatUser> wechatUsers = wechatWorkService.getAllUsers();
    
    // 2. 同步到Metabase
    for (WechatUser wechatUser : wechatUsers) {
        syncUserToMetabase(wechatUser);
    }
}
```

## 🎨 前端集成

### 登录页面改造
```html
<!-- 在Metabase登录页面添加企业微信登录按钮 -->
<div class="wechat-login-container">
    <button onclick="loginWithWechat()" class="wechat-login-btn">
        <img src="/images/wechat-work-icon.png" alt="企业微信">
        企业微信登录
    </button>
</div>

<script>
function loginWithWechat() {
    window.location.href = '/auth/wechat/login';
}
</script>
```

## 📋 实施计划

### 第一阶段：基础集成（1-2周）
- [ ] 企业微信应用创建和配置
- [ ] 认证服务开发
- [ ] 基础用户映射功能

### 第二阶段：权限同步（1周）
- [ ] 权限映射规则设计
- [ ] 自动权限分配功能
- [ ] 权限同步定时任务

### 第三阶段：优化完善（1周）
- [ ] 前端UI优化
- [ ] 监控和日志完善
- [ ] 性能优化和测试

## 🔍 测试方案

### 功能测试
- 企业微信登录流程测试
- 用户信息同步测试
- 权限分配测试

### 性能测试
- 并发登录测试
- 认证响应时间测试
- 系统稳定性测试

### 安全测试
- Token安全性测试
- 会话管理测试
- 权限控制测试

## 📞 技术支持

### 常见问题
1. **企业微信回调域名配置**
2. **Metabase用户权限映射**
3. **Token过期处理**
4. **跨域问题解决**

### 联系方式
- 技术支持：开发团队
- 文档更新：定期维护

## 💻 核心代码实现

### 企业微信认证服务实现

#### 1. 配置类
```java
@Configuration
@ConfigurationProperties(prefix = "wechat.work")
@Data
public class WechatWorkProperties {
    private String corpId;
    private String corpSecret;
    private String agentId;
    private String redirectUri;
    private String baseUrl = "https://qyapi.weixin.qq.com";
}
```

#### 2. 企业微信API服务
```java
@Service
@Slf4j
public class WechatWorkApiService {

    @Autowired
    private WechatWorkProperties properties;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取访问令牌
     */
    public String getAccessToken() {
        String cacheKey = "wechat_work_access_token";
        String accessToken = (String) redisTemplate.opsForValue().get(cacheKey);

        if (StringUtils.hasText(accessToken)) {
            return accessToken;
        }

        String url = properties.getBaseUrl() + "/cgi-bin/gettoken" +
                "?corpid=" + properties.getCorpId() +
                "&corpsecret=" + properties.getCorpSecret();

        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> result = response.getBody();

            if (result != null && "0".equals(String.valueOf(result.get("errcode")))) {
                accessToken = (String) result.get("access_token");
                Integer expiresIn = (Integer) result.get("expires_in");

                // 缓存token，提前5分钟过期
                redisTemplate.opsForValue().set(cacheKey, accessToken,
                    Duration.ofSeconds(expiresIn - 300));

                return accessToken;
            } else {
                throw new RuntimeException("获取企业微信访问令牌失败: " + result.get("errmsg"));
            }
        } catch (Exception e) {
            log.error("获取企业微信访问令牌异常", e);
            throw new RuntimeException("获取企业微信访问令牌异常", e);
        }
    }

    /**
     * 通过code获取用户信息
     */
    public WechatWorkUser getUserInfoByCode(String code) {
        String accessToken = getAccessToken();
        String url = properties.getBaseUrl() + "/cgi-bin/auth/getuserinfo" +
                "?access_token=" + accessToken + "&code=" + code;

        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> result = response.getBody();

            if (result != null && "0".equals(String.valueOf(result.get("errcode")))) {
                String userId = (String) result.get("userid");
                return getUserDetail(userId);
            } else {
                throw new RuntimeException("获取用户信息失败: " + result.get("errmsg"));
            }
        } catch (Exception e) {
            log.error("通过code获取用户信息异常", e);
            throw new RuntimeException("通过code获取用户信息异常", e);
        }
    }

    /**
     * 获取用户详细信息
     */
    public WechatWorkUser getUserDetail(String userId) {
        String accessToken = getAccessToken();
        String url = properties.getBaseUrl() + "/cgi-bin/user/get" +
                "?access_token=" + accessToken + "&userid=" + userId;

        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> result = response.getBody();

            if (result != null && "0".equals(String.valueOf(result.get("errcode")))) {
                WechatWorkUser user = new WechatWorkUser();
                user.setUserId((String) result.get("userid"));
                user.setName((String) result.get("name"));
                user.setEmail((String) result.get("email"));
                user.setMobile((String) result.get("mobile"));
                user.setDepartment((List<Integer>) result.get("department"));
                user.setPosition((String) result.get("position"));
                return user;
            } else {
                throw new RuntimeException("获取用户详细信息失败: " + result.get("errmsg"));
            }
        } catch (Exception e) {
            log.error("获取用户详细信息异常", e);
            throw new RuntimeException("获取用户详细信息异常", e);
        }
    }
}
```

#### 3. Metabase用户管理服务
```java
@Service
@Slf4j
public class MetabaseUserService {

    @Autowired
    private WechatMetabaseUserMappingRepository userMappingRepository;

    @Autowired
    private MetabaseApiService metabaseApiService;

    /**
     * 创建或更新Metabase用户
     */
    public MetabaseUser createOrUpdateUser(WechatWorkUser wechatUser) {
        // 查找现有映射
        Optional<WechatMetabaseUserMapping> mappingOpt =
            userMappingRepository.findByWechatUserid(wechatUser.getUserId());

        MetabaseUser metabaseUser;

        if (mappingOpt.isPresent()) {
            // 更新现有用户
            WechatMetabaseUserMapping mapping = mappingOpt.get();
            metabaseUser = metabaseApiService.updateUser(
                mapping.getMetabaseUserId(), wechatUser);

            // 更新映射信息
            mapping.setWechatName(wechatUser.getName());
            mapping.setWechatEmail(wechatUser.getEmail());
            mapping.setUpdatedTime(LocalDateTime.now());
            userMappingRepository.save(mapping);

        } else {
            // 创建新用户
            metabaseUser = metabaseApiService.createUser(wechatUser);

            // 创建映射关系
            WechatMetabaseUserMapping mapping = new WechatMetabaseUserMapping();
            mapping.setWechatUserid(wechatUser.getUserId());
            mapping.setWechatName(wechatUser.getName());
            mapping.setWechatEmail(wechatUser.getEmail());
            mapping.setWechatDepartment(getDepartmentNames(wechatUser.getDepartment()));
            mapping.setMetabaseUserId(metabaseUser.getId());
            mapping.setMetabaseEmail(metabaseUser.getEmail());
            userMappingRepository.save(mapping);

            // 分配权限
            assignUserPermissions(wechatUser, metabaseUser);
        }

        return metabaseUser;
    }

    /**
     * 根据部门分配权限
     */
    private void assignUserPermissions(WechatWorkUser wechatUser, MetabaseUser metabaseUser) {
        String departmentNames = getDepartmentNames(wechatUser.getDepartment());

        // 根据部门查找权限映射
        List<WechatMetabasePermissionMapping> permissionMappings =
            permissionMappingRepository.findByWechatDepartment(departmentNames);

        for (WechatMetabasePermissionMapping mapping : permissionMappings) {
            metabaseApiService.addUserToGroup(metabaseUser.getId(), mapping.getMetabaseGroupId());
        }
    }

    private String getDepartmentNames(List<Integer> departmentIds) {
        // 实现部门ID到部门名称的转换
        return departmentIds.stream()
            .map(String::valueOf)
            .collect(Collectors.joining(","));
    }
}
```

### JWT Token生成和验证

#### JWT工具类
```java
@Component
@Slf4j
public class JwtTokenUtil {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration:7200}") // 默认2小时
    private Long expiration;

    /**
     * 生成JWT Token
     */
    public String generateToken(MetabaseUser user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("email", user.getEmail());
        claims.put("name", user.getName());
        claims.put("source", "wechat_work");

        return createToken(claims, user.getEmail());
    }

    private String createToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 验证Token
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(secret).parseClaimsJws(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.error("JWT token验证失败", e);
            return false;
        }
    }

    /**
     * 从Token获取用户信息
     */
    public Claims getClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }
}
```

## 🔧 配置文件示例

### application.yml
```yaml
# 企业微信配置
wechat:
  work:
    corp-id: ${WECHAT_WORK_CORP_ID:your_corp_id}
    corp-secret: ${WECHAT_WORK_CORP_SECRET:your_corp_secret}
    agent-id: ${WECHAT_WORK_AGENT_ID:your_agent_id}
    redirect-uri: ${WECHAT_WORK_REDIRECT_URI:http://localhost/auth/wechat/callback}
    base-url: https://qyapi.weixin.qq.com

# JWT配置
jwt:
  secret: ${JWT_SECRET:your_jwt_secret_key}
  expiration: 7200 # 2小时

# Metabase配置
metabase:
  base-url: ${METABASE_BASE_URL:http://localhost:3000}
  admin-email: ${METABASE_ADMIN_EMAIL:<EMAIL>}
  admin-password: ${METABASE_ADMIN_PASSWORD:admin_password}

# Redis配置
spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

---

*本方案基于现有项目架构设计，可根据实际需求进行调整和优化。*
