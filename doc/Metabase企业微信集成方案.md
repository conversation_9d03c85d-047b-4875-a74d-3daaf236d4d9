# Metabase企业微信集成方案（开源版适配）

## 📋 方案概述

由于Metabase开源版不支持SSO，只支持LDAP认证，本方案提供三种可行的集成方式，实现企业微信与Metabase的集成。

## 🎯 集成目标

1. **统一身份认证**: 通过企业微信身份访问Metabase
2. **用户信息同步**: 自动同步企业微信用户到Metabase
3. **权限管理**: 基于企业微信组织架构分配权限
4. **简化用户体验**: 减少用户登录步骤

## 🏗️ 可选方案对比

| 方案 | 复杂度 | 用户体验 | 维护成本 | 推荐指数 |
|------|--------|----------|----------|----------|
| 方案一：LDAP桥接 | 中等 | 好 | 低 | ⭐⭐⭐⭐⭐ |
| 方案二：反向代理拦截 | 高 | 很好 | 中等 | ⭐⭐⭐⭐ |
| 方案三：自动登录脚本 | 低 | 一般 | 低 | ⭐⭐⭐ |

## 🔧 方案一：LDAP桥接服务（推荐）

### 架构设计
```
企业微信 → LDAP桥接服务 → Metabase LDAP认证
    ↓           ↓              ↓
  用户信息   LDAP协议转换    用户登录
  组织架构   权限映射        权限分配
```

### 核心思路
1. 搭建一个LDAP服务器（如OpenLDAP）
2. 创建企业微信到LDAP的数据同步服务
3. Metabase配置LDAP认证
4. 用户使用企业微信账号信息登录Metabase

#### 技术栈
- **OpenLDAP**: LDAP服务器
- **Spring Boot**: 数据同步服务
- **MySQL**: 用户映射存储
- **定时任务**: 用户信息同步

#### 实现步骤

##### 1. 安装配置OpenLDAP
```bash
# Ubuntu/Debian安装
sudo apt update
sudo apt install slapd ldap-utils

# 基础配置
sudo dpkg-reconfigure slapd
```

##### 2. LDAP目录结构设计
```ldif
# 组织结构
dn: dc=company,dc=com
objectClass: dcObject
objectClass: organization
o: Company
dc: company

# 用户组织单元
dn: ou=people,dc=company,dc=com
objectClass: organizationalUnit
ou: people

# 用户组组织单元
dn: ou=groups,dc=company,dc=com
objectClass: organizationalUnit
ou: groups

# 示例用户
dn: uid=zhangsan,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
uid: zhangsan
cn: 张三
sn: 张
givenName: 三
mail: <EMAIL>
userPassword: {SSHA}encrypted_password
uidNumber: 1001
gidNumber: 1001
homeDirectory: /home/<USER>

# 示例用户组
dn: cn=metabase_admin,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: metabase_admin
member: uid=zhangsan,ou=people,dc=company,dc=com
```

##### 3. 企业微信同步服务开发
```java
@Service
@Slf4j
public class WechatToLdapSyncService {

    @Autowired
    private WechatWorkApiService wechatWorkApiService;

    @Autowired
    private LdapTemplate ldapTemplate;

    /**
     * 同步企业微信用户到LDAP
     */
    @Scheduled(fixedRate = 3600000) // 每小时同步一次
    public void syncUsersFromWechatToLdap() {
        try {
            // 1. 获取企业微信所有用户
            List<WechatWorkUser> wechatUsers = wechatWorkApiService.getAllUsers();

            // 2. 同步到LDAP
            for (WechatWorkUser wechatUser : wechatUsers) {
                syncUserToLdap(wechatUser);
            }

            log.info("企业微信用户同步完成，共同步{}个用户", wechatUsers.size());
        } catch (Exception e) {
            log.error("企业微信用户同步失败", e);
        }
    }

    private void syncUserToLdap(WechatWorkUser wechatUser) {
        try {
            // 构建LDAP用户对象
            LdapUser ldapUser = buildLdapUser(wechatUser);

            // 检查用户是否存在
            String userDn = "uid=" + wechatUser.getUserId() + ",ou=people,dc=company,dc=com";

            if (ldapTemplate.search(query().where("uid").is(wechatUser.getUserId())).isEmpty()) {
                // 创建新用户
                ldapTemplate.create(ldapUser);
                log.info("创建LDAP用户: {}", wechatUser.getUserId());
            } else {
                // 更新现有用户
                ldapTemplate.update(ldapUser);
                log.info("更新LDAP用户: {}", wechatUser.getUserId());
            }

            // 同步用户组
            syncUserGroups(wechatUser);

        } catch (Exception e) {
            log.error("同步用户{}到LDAP失败", wechatUser.getUserId(), e);
        }
    }

    private LdapUser buildLdapUser(WechatWorkUser wechatUser) {
        LdapUser ldapUser = new LdapUser();
        ldapUser.setUid(wechatUser.getUserId());
        ldapUser.setCn(wechatUser.getName());
        ldapUser.setSn(extractSurname(wechatUser.getName()));
        ldapUser.setGivenName(extractGivenName(wechatUser.getName()));
        ldapUser.setMail(wechatUser.getEmail());
        ldapUser.setUserPassword(generateDefaultPassword()); // 生成默认密码
        ldapUser.setUidNumber(generateUidNumber());
        ldapUser.setGidNumber(1001); // 默认组
        ldapUser.setHomeDirectory("/home/" + wechatUser.getUserId());
        return ldapUser;
    }
}
```

##### 4. Metabase LDAP配置
在Metabase管理界面配置LDAP：
```bash
# LDAP设置
LDAP Host: localhost:389
LDAP Port: 389
LDAP Security: None (或 StartTLS)
LDAP Bind DN: cn=admin,dc=company,dc=com
LDAP Bind Password: your_admin_password

# 用户搜索配置
User Base: ou=people,dc=company,dc=com
User Filter: (uid={login})

# 属性映射
Email Attribute: mail
First Name Attribute: givenName
Last Name Attribute: sn

# 用户组配置
Group Base: ou=groups,dc=company,dc=com
Group Filter: (member={dn})
```

## 🔧 方案二：反向代理拦截登录

### 架构设计
```
用户 → Nginx → 企业微信认证服务 → 自动登录Metabase
 ↓       ↓           ↓                    ↓
访问   拦截请求   获取用户信息        模拟登录表单
```

### 实现思路
1. Nginx拦截所有Metabase访问请求
2. 未认证用户重定向到企业微信登录
3. 认证成功后自动填充Metabase登录表单
4. 后台自动提交登录请求

### 核心代码示例
```java
@RestController
@RequestMapping("/proxy")
public class MetabaseProxyController {

    @GetMapping("/login")
    public ResponseEntity<String> proxyLogin(HttpServletRequest request) {
        // 1. 检查企业微信认证状态
        String wechatToken = getWechatTokenFromCookie(request);
        if (StringUtils.isEmpty(wechatToken)) {
            return redirectToWechatAuth();
        }

        // 2. 获取用户信息
        WechatWorkUser user = getUserFromToken(wechatToken);

        // 3. 自动登录Metabase
        String metabaseSession = autoLoginMetabase(user);

        // 4. 重定向到Metabase主页
        return ResponseEntity.status(302)
            .header("Location", "/metabase/dashboard")
            .header("Set-Cookie", "metabase.SESSION=" + metabaseSession)
            .build();
    }

    private String autoLoginMetabase(WechatWorkUser user) {
        // 构建登录请求
        Map<String, String> loginData = new HashMap<>();
        loginData.put("username", user.getEmail());
        loginData.put("password", getOrCreateUserPassword(user));

        // 调用Metabase登录API
        ResponseEntity<Map> response = restTemplate.postForEntity(
            "http://localhost:3000/api/session",
            loginData,
            Map.class
        );

        return (String) response.getBody().get("id");
    }
}
```

## 🔧 方案三：用户引导式登录

### 实现思路
1. 创建企业微信登录入口页面
2. 用户通过企业微信认证后显示Metabase登录信息
3. 用户手动登录Metabase（密码自动生成并显示）

### 用户体验流程
```
1. 用户访问企业微信登录页面
2. 扫码或点击授权登录
3. 显示Metabase登录信息页面
4. 用户复制信息到Metabase登录
5. 首次登录后可保存密码
```

### 实现代码
```java
@Controller
@RequestMapping("/wechat-metabase")
public class WechatMetabaseController {

    @GetMapping("/login")
    public String showLoginPage() {
        return "wechat-login";
    }

    @GetMapping("/callback")
    public String handleCallback(@RequestParam String code, Model model) {
        // 1. 获取企业微信用户信息
        WechatWorkUser user = wechatWorkApiService.getUserInfoByCode(code);

        // 2. 创建或获取Metabase用户信息
        MetabaseUserInfo metabaseUser = createOrGetMetabaseUser(user);

        // 3. 传递给前端页面
        model.addAttribute("email", metabaseUser.getEmail());
        model.addAttribute("password", metabaseUser.getPassword());
        model.addAttribute("metabaseUrl", metabaseProperties.getBaseUrl());

        return "metabase-login-info";
    }
}
```

### 前端页面模板
```html
<!-- metabase-login-info.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Metabase登录信息</title>
    <style>
        .login-info {
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .copy-btn {
            margin-left: 10px;
            padding: 5px 10px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="login-info">
        <h2>Metabase登录信息</h2>
        <p>请使用以下信息登录Metabase：</p>

        <div>
            <label>邮箱：</label>
            <input type="text" id="email" value="${email}" readonly>
            <button class="copy-btn" onclick="copyToClipboard('email')">复制</button>
        </div>

        <div>
            <label>密码：</label>
            <input type="text" id="password" value="${password}" readonly>
            <button class="copy-btn" onclick="copyToClipboard('password')">复制</button>
        </div>

        <div style="margin-top: 20px;">
            <a href="${metabaseUrl}" target="_blank" class="login-btn">
                打开Metabase登录页面
            </a>
        </div>

        <div style="margin-top: 15px; color: #666; font-size: 14px;">
            <p>提示：首次登录后，建议在浏览器中保存密码以便后续使用。</p>
        </div>
    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            document.execCommand('copy');
            alert('已复制到剪贴板');
        }
    </script>
</body>
</html>
```

## 📊 数据库设计

### 用户映射表
```sql
CREATE TABLE wechat_metabase_user_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    wechat_userid VARCHAR(64) NOT NULL UNIQUE,
    wechat_name VARCHAR(100),
    wechat_email VARCHAR(100),
    wechat_department VARCHAR(200),
    metabase_user_id INT,
    metabase_email VARCHAR(100),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_wechat_userid (wechat_userid),
    INDEX idx_metabase_user_id (metabase_user_id)
);
```

### 权限映射表
```sql
CREATE TABLE wechat_metabase_permission_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    wechat_department VARCHAR(200),
    metabase_group_id INT,
    metabase_group_name VARCHAR(100),
    permission_level ENUM('viewer', 'editor', 'admin') DEFAULT 'viewer',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_department (wechat_department)
);
```

## 🔐 安全考虑

### 1. Token安全
- JWT Token设置合理过期时间（建议2小时）
- 使用强密钥签名
- 支持Token刷新机制

### 2. 会话管理
- Redis存储会话信息
- 设置会话超时
- 支持强制登出

### 3. 权限控制
- 基于企业微信部门自动分配权限
- 支持细粒度权限控制
- 定期同步权限变更

## 🚀 部署方案

### 开发环境部署
```bash
# 1. 启动认证服务
cd auth-service
mvn spring-boot:run

# 2. 配置Nginx
sudo nginx -s reload

# 3. 启动Metabase
java -jar metabase.jar
```

### 生产环境部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  auth-service:
    build: ./auth-service
    ports:
      - "8080:8080"
    environment:
      - WECHAT_WORK_CORP_ID=${CORP_ID}
      - WECHAT_WORK_CORP_SECRET=${CORP_SECRET}
    depends_on:
      - redis
      - mysql
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - auth-service
      - metabase
  
  metabase:
    image: metabase/metabase:latest
    ports:
      - "3000:3000"
    environment:
      - MB_DB_TYPE=mysql
      - MB_DB_HOST=mysql
    depends_on:
      - mysql
  
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
  
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=metabase
    ports:
      - "3306:3306"
```

## 📈 监控和日志

### 1. 认证日志
```java
@Slf4j
@Component
public class AuthenticationLogger {
    
    public void logAuthSuccess(String userId, String source) {
        log.info("Authentication success: userId={}, source={}", userId, source);
    }
    
    public void logAuthFailure(String userId, String reason) {
        log.warn("Authentication failed: userId={}, reason={}", userId, reason);
    }
}
```

### 2. 性能监控
- 认证响应时间监控
- 用户登录频率统计
- 错误率监控

## 🔄 用户同步流程

### 自动同步
```java
@Scheduled(fixedRate = 3600000) // 每小时同步一次
public void syncWechatUsers() {
    // 1. 获取企业微信用户列表
    List<WechatUser> wechatUsers = wechatWorkService.getAllUsers();
    
    // 2. 同步到Metabase
    for (WechatUser wechatUser : wechatUsers) {
        syncUserToMetabase(wechatUser);
    }
}
```

## 🎨 前端集成

### 登录页面改造
```html
<!-- 在Metabase登录页面添加企业微信登录按钮 -->
<div class="wechat-login-container">
    <button onclick="loginWithWechat()" class="wechat-login-btn">
        <img src="/images/wechat-work-icon.png" alt="企业微信">
        企业微信登录
    </button>
</div>

<script>
function loginWithWechat() {
    window.location.href = '/auth/wechat/login';
}
</script>
```

## 📋 实施计划

### 第一阶段：基础集成（1-2周）
- [ ] 企业微信应用创建和配置
- [ ] 认证服务开发
- [ ] 基础用户映射功能

### 第二阶段：权限同步（1周）
- [ ] 权限映射规则设计
- [ ] 自动权限分配功能
- [ ] 权限同步定时任务

### 第三阶段：优化完善（1周）
- [ ] 前端UI优化
- [ ] 监控和日志完善
- [ ] 性能优化和测试

## 🔍 测试方案

### 功能测试
- 企业微信登录流程测试
- 用户信息同步测试
- 权限分配测试

### 性能测试
- 并发登录测试
- 认证响应时间测试
- 系统稳定性测试

### 安全测试
- Token安全性测试
- 会话管理测试
- 权限控制测试

## 📞 技术支持

### 常见问题
1. **企业微信回调域名配置**
2. **Metabase用户权限映射**
3. **Token过期处理**
4. **跨域问题解决**

### 联系方式
- 技术支持：开发团队
- 文档更新：定期维护

## 💻 核心代码实现

### 企业微信认证服务实现

#### 1. 配置类
```java
@Configuration
@ConfigurationProperties(prefix = "wechat.work")
@Data
public class WechatWorkProperties {
    private String corpId;
    private String corpSecret;
    private String agentId;
    private String redirectUri;
    private String baseUrl = "https://qyapi.weixin.qq.com";
}
```

#### 2. 企业微信API服务
```java
@Service
@Slf4j
public class WechatWorkApiService {

    @Autowired
    private WechatWorkProperties properties;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取访问令牌
     */
    public String getAccessToken() {
        String cacheKey = "wechat_work_access_token";
        String accessToken = (String) redisTemplate.opsForValue().get(cacheKey);

        if (StringUtils.hasText(accessToken)) {
            return accessToken;
        }

        String url = properties.getBaseUrl() + "/cgi-bin/gettoken" +
                "?corpid=" + properties.getCorpId() +
                "&corpsecret=" + properties.getCorpSecret();

        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> result = response.getBody();

            if (result != null && "0".equals(String.valueOf(result.get("errcode")))) {
                accessToken = (String) result.get("access_token");
                Integer expiresIn = (Integer) result.get("expires_in");

                // 缓存token，提前5分钟过期
                redisTemplate.opsForValue().set(cacheKey, accessToken,
                    Duration.ofSeconds(expiresIn - 300));

                return accessToken;
            } else {
                throw new RuntimeException("获取企业微信访问令牌失败: " + result.get("errmsg"));
            }
        } catch (Exception e) {
            log.error("获取企业微信访问令牌异常", e);
            throw new RuntimeException("获取企业微信访问令牌异常", e);
        }
    }

    /**
     * 通过code获取用户信息
     */
    public WechatWorkUser getUserInfoByCode(String code) {
        String accessToken = getAccessToken();
        String url = properties.getBaseUrl() + "/cgi-bin/auth/getuserinfo" +
                "?access_token=" + accessToken + "&code=" + code;

        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> result = response.getBody();

            if (result != null && "0".equals(String.valueOf(result.get("errcode")))) {
                String userId = (String) result.get("userid");
                return getUserDetail(userId);
            } else {
                throw new RuntimeException("获取用户信息失败: " + result.get("errmsg"));
            }
        } catch (Exception e) {
            log.error("通过code获取用户信息异常", e);
            throw new RuntimeException("通过code获取用户信息异常", e);
        }
    }

    /**
     * 获取用户详细信息
     */
    public WechatWorkUser getUserDetail(String userId) {
        String accessToken = getAccessToken();
        String url = properties.getBaseUrl() + "/cgi-bin/user/get" +
                "?access_token=" + accessToken + "&userid=" + userId;

        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> result = response.getBody();

            if (result != null && "0".equals(String.valueOf(result.get("errcode")))) {
                WechatWorkUser user = new WechatWorkUser();
                user.setUserId((String) result.get("userid"));
                user.setName((String) result.get("name"));
                user.setEmail((String) result.get("email"));
                user.setMobile((String) result.get("mobile"));
                user.setDepartment((List<Integer>) result.get("department"));
                user.setPosition((String) result.get("position"));
                return user;
            } else {
                throw new RuntimeException("获取用户详细信息失败: " + result.get("errmsg"));
            }
        } catch (Exception e) {
            log.error("获取用户详细信息异常", e);
            throw new RuntimeException("获取用户详细信息异常", e);
        }
    }
}
```

#### 3. Metabase用户管理服务
```java
@Service
@Slf4j
public class MetabaseUserService {

    @Autowired
    private WechatMetabaseUserMappingRepository userMappingRepository;

    @Autowired
    private MetabaseApiService metabaseApiService;

    /**
     * 创建或更新Metabase用户
     */
    public MetabaseUser createOrUpdateUser(WechatWorkUser wechatUser) {
        // 查找现有映射
        Optional<WechatMetabaseUserMapping> mappingOpt =
            userMappingRepository.findByWechatUserid(wechatUser.getUserId());

        MetabaseUser metabaseUser;

        if (mappingOpt.isPresent()) {
            // 更新现有用户
            WechatMetabaseUserMapping mapping = mappingOpt.get();
            metabaseUser = metabaseApiService.updateUser(
                mapping.getMetabaseUserId(), wechatUser);

            // 更新映射信息
            mapping.setWechatName(wechatUser.getName());
            mapping.setWechatEmail(wechatUser.getEmail());
            mapping.setUpdatedTime(LocalDateTime.now());
            userMappingRepository.save(mapping);

        } else {
            // 创建新用户
            metabaseUser = metabaseApiService.createUser(wechatUser);

            // 创建映射关系
            WechatMetabaseUserMapping mapping = new WechatMetabaseUserMapping();
            mapping.setWechatUserid(wechatUser.getUserId());
            mapping.setWechatName(wechatUser.getName());
            mapping.setWechatEmail(wechatUser.getEmail());
            mapping.setWechatDepartment(getDepartmentNames(wechatUser.getDepartment()));
            mapping.setMetabaseUserId(metabaseUser.getId());
            mapping.setMetabaseEmail(metabaseUser.getEmail());
            userMappingRepository.save(mapping);

            // 分配权限
            assignUserPermissions(wechatUser, metabaseUser);
        }

        return metabaseUser;
    }

    /**
     * 根据部门分配权限
     */
    private void assignUserPermissions(WechatWorkUser wechatUser, MetabaseUser metabaseUser) {
        String departmentNames = getDepartmentNames(wechatUser.getDepartment());

        // 根据部门查找权限映射
        List<WechatMetabasePermissionMapping> permissionMappings =
            permissionMappingRepository.findByWechatDepartment(departmentNames);

        for (WechatMetabasePermissionMapping mapping : permissionMappings) {
            metabaseApiService.addUserToGroup(metabaseUser.getId(), mapping.getMetabaseGroupId());
        }
    }

    private String getDepartmentNames(List<Integer> departmentIds) {
        // 实现部门ID到部门名称的转换
        return departmentIds.stream()
            .map(String::valueOf)
            .collect(Collectors.joining(","));
    }
}
```

### JWT Token生成和验证

#### JWT工具类
```java
@Component
@Slf4j
public class JwtTokenUtil {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration:7200}") // 默认2小时
    private Long expiration;

    /**
     * 生成JWT Token
     */
    public String generateToken(MetabaseUser user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("email", user.getEmail());
        claims.put("name", user.getName());
        claims.put("source", "wechat_work");

        return createToken(claims, user.getEmail());
    }

    private String createToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 验证Token
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(secret).parseClaimsJws(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.error("JWT token验证失败", e);
            return false;
        }
    }

    /**
     * 从Token获取用户信息
     */
    public Claims getClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }
}
```

## 🔧 配置文件示例

### application.yml
```yaml
# 企业微信配置
wechat:
  work:
    corp-id: ${WECHAT_WORK_CORP_ID:your_corp_id}
    corp-secret: ${WECHAT_WORK_CORP_SECRET:your_corp_secret}
    agent-id: ${WECHAT_WORK_AGENT_ID:your_agent_id}
    redirect-uri: ${WECHAT_WORK_REDIRECT_URI:http://localhost/auth/wechat/callback}
    base-url: https://qyapi.weixin.qq.com

# JWT配置
jwt:
  secret: ${JWT_SECRET:your_jwt_secret_key}
  expiration: 7200 # 2小时

# Metabase配置
metabase:
  base-url: ${METABASE_BASE_URL:http://localhost:3000}
  admin-email: ${METABASE_ADMIN_EMAIL:<EMAIL>}
  admin-password: ${METABASE_ADMIN_PASSWORD:admin_password}

# Redis配置
spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

---

*本方案基于现有项目架构设计，可根据实际需求进行调整和优化。*
