# Metabase企业微信集成快速实施指南（LDAP方案）

## 🚀 快速开始

### 前置条件检查
- [ ] Metabase已部署并正常运行
- [ ] 企业微信管理员权限
- [ ] Java开发环境（JDK 8+）
- [ ] Linux服务器（用于安装OpenLDAP）
- [ ] MySQL数据库

## 📋 推荐方案：LDAP桥接

由于Metabase开源版不支持SSO，只支持LDAP认证，我们采用LDAP桥接方案：
```
企业微信 → 同步服务 → OpenLDAP → Metabase LDAP认证
```

## 📋 实施步骤

### 第一步：企业微信配置（30分钟）

#### 1.1 创建企业应用
1. 登录企业微信管理后台
2. 进入"应用管理" → "自建应用"
3. 创建新应用，记录以下信息：
   ```
   CorpID: ww1234567890abcdef
   AgentID: 1000001
   Secret: your_app_secret
   ```

#### 1.2 获取通讯录权限
1. 在应用详情页面，点击"企业可信IP"
2. 添加服务器IP地址
3. 确保应用有"通讯录"的读取权限

### 第二步：安装配置OpenLDAP（45分钟）

#### 2.1 安装OpenLDAP
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install slapd ldap-utils

# CentOS/RHEL
sudo yum install openldap-servers openldap-clients

# 配置管理员密码
sudo dpkg-reconfigure slapd
```

#### 2.2 基础配置
```bash
# 设置域名：company.com
# 组织名称：Company
# 管理员密码：设置强密码
```

#### 2.3 创建LDAP目录结构
创建文件 `base.ldif`：
```ldif
# 创建组织单元
dn: ou=people,dc=company,dc=com
objectClass: organizationalUnit
ou: people

dn: ou=groups,dc=company,dc=com
objectClass: organizationalUnit
ou: groups

# 创建管理员组
dn: cn=metabase_admin,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: metabase_admin
member: cn=admin,dc=company,dc=com

# 创建普通用户组
dn: cn=metabase_user,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: metabase_user
member: cn=admin,dc=company,dc=com
```

导入配置：
```bash
sudo ldapadd -x -D "cn=admin,dc=company,dc=com" -W -f base.ldif
```

#### 2.4 验证LDAP服务
```bash
# 测试连接
ldapsearch -x -H ldap://localhost -b "dc=company,dc=com" -D "cn=admin,dc=company,dc=com" -W

# 查看组织结构
ldapsearch -x -H ldap://localhost -b "ou=people,dc=company,dc=com"
```

### 第三步：开发同步服务（2小时）

#### 3.1 创建数据库表
```sql
-- 企业微信用户同步记录表
CREATE TABLE wechat_ldap_user_sync (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    wechat_userid VARCHAR(64) NOT NULL UNIQUE,
    wechat_name VARCHAR(100),
    wechat_email VARCHAR(100),
    wechat_mobile VARCHAR(20),
    wechat_department VARCHAR(200),
    ldap_dn VARCHAR(500),
    sync_status ENUM('success', 'failed') DEFAULT 'success',
    last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_wechat_userid (wechat_userid),
    INDEX idx_sync_time (last_sync_time)
);

-- 部门权限映射表
CREATE TABLE department_permission_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    department_name VARCHAR(200) NOT NULL,
    ldap_group_dn VARCHAR(500) NOT NULL,
    permission_level ENUM('admin', 'user') DEFAULT 'user',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_department (department_name)
);

-- 插入默认权限映射
INSERT INTO department_permission_mapping (department_name, ldap_group_dn, permission_level) VALUES
('技术部', 'cn=metabase_admin,ou=groups,dc=company,dc=com', 'admin'),
('产品部', 'cn=metabase_user,ou=groups,dc=company,dc=com', 'user'),
('运营部', 'cn=metabase_user,ou=groups,dc=company,dc=com', 'user');
```

#### 3.2 添加依赖（pom.xml）
```xml
<!-- Spring LDAP -->
<dependency>
    <groupId>org.springframework.ldap</groupId>
    <artifactId>spring-ldap-core</artifactId>
</dependency>

<!-- HTTP客户端 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- 定时任务 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-quartz</artifactId>
</dependency>
```

#### 3.3 核心代码文件清单
```
cash-manage-system/src/main/java/com/jinghang/cash/modules/ldap/
├── config/
│   ├── LdapConfig.java                # LDAP配置
│   └── WechatWorkConfig.java          # 企业微信配置
├── service/
│   ├── WechatWorkApiService.java      # 企业微信API服务
│   ├── LdapUserSyncService.java       # LDAP用户同步服务
│   └── WechatToLdapSyncService.java   # 企业微信到LDAP同步服务
├── entity/
│   ├── WechatLdapUserSync.java        # 同步记录实体
│   └── DepartmentPermissionMapping.java # 权限映射实体
├── repository/
│   ├── WechatLdapUserSyncRepository.java
│   └── DepartmentPermissionMappingRepository.java
└── dto/
    ├── WechatWorkUser.java            # 企业微信用户DTO
    └── LdapUser.java                  # LDAP用户DTO
```

### 第四步：配置Metabase LDAP认证（30分钟）

#### 4.1 登录Metabase管理界面
1. 访问 `http://your-metabase-url/admin`
2. 使用管理员账号登录
3. 进入 "Settings" → "Authentication"

#### 4.2 配置LDAP设置
```bash
# LDAP服务器设置
LDAP Host: localhost
LDAP Port: 389
LDAP Security: None (或根据你的配置选择)

# 绑定账号（用于搜索用户）
LDAP Bind DN: cn=admin,dc=company,dc=com
LDAP Bind Password: your_admin_password

# 用户搜索设置
User Base: ou=people,dc=company,dc=com
User Filter: (uid={login})

# 属性映射
Email Attribute: mail
First Name Attribute: givenName
Last Name Attribute: sn

# 用户组设置（可选）
Group Base: ou=groups,dc=company,dc=com
Group Filter: (member={dn})
```

#### 4.3 测试LDAP连接
1. 在Metabase LDAP配置页面点击"Test Connection"
2. 确保连接成功
3. 保存配置

### 第五步：配置文件设置（15分钟）

#### 5.1 Apollo配置（推荐）
在Apollo配置中心添加：
```properties
# 企业微信配置
wechat.work.corp-id=ww1234567890abcdef
wechat.work.corp-secret=your_corp_secret
wechat.work.agent-id=1000001

# LDAP配置
ldap.url=ldap://localhost:389
ldap.base=dc=company,dc=com
ldap.username=cn=admin,dc=company,dc=com
ldap.password=your_admin_password
ldap.user-base=ou=people,dc=company,dc=com
ldap.group-base=ou=groups,dc=company,dc=com

# 同步配置
sync.enabled=true
sync.interval=3600000
```

#### 5.2 本地配置（开发环境）
在 `application-dev.yml` 中添加：
```yaml
wechat:
  work:
    corp-id: ww1234567890abcdef
    corp-secret: your_corp_secret
    agent-id: 1000001
    base-url: https://qyapi.weixin.qq.com

ldap:
  url: ldap://localhost:389
  base: dc=company,dc=com
  username: cn=admin,dc=company,dc=com
  password: your_admin_password
  user-base: ou=people,dc=company,dc=com
  group-base: ou=groups,dc=company,dc=com

sync:
  enabled: true
  interval: 3600000 # 1小时同步一次
```

### 第六步：测试验证（30分钟）

#### 6.1 启动服务
```bash
# 启动同步服务
cd jh-loan-cash-manage
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 确保LDAP服务运行
sudo systemctl status slapd

# 启动Metabase（如果未启动）
java -jar metabase.jar
```

#### 6.2 手动触发同步测试
```bash
# 调用同步接口（如果提供了手动触发接口）
curl -X POST http://localhost:9006/admin/api/ldap/sync

# 或者查看定时任务日志
tail -f logs/cash-manage.log | grep "sync"
```

#### 6.3 验证LDAP用户
```bash
# 查看同步的用户
ldapsearch -x -H ldap://localhost -b "ou=people,dc=company,dc=com" -D "cn=admin,dc=company,dc=com" -W

# 查看用户组
ldapsearch -x -H ldap://localhost -b "ou=groups,dc=company,dc=com" -D "cn=admin,dc=company,dc=com" -W
```

#### 6.4 测试Metabase登录
1. 访问Metabase登录页面
2. 使用企业微信用户的邮箱登录
3. 密码使用同步时生成的默认密码
4. 验证用户权限是否正确

## 🔧 常见问题解决

### 问题1：企业微信回调失败
**症状**：点击登录后无响应或报错
**解决**：
1. 检查可信域名配置
2. 确认回调URL正确
3. 验证企业微信应用配置

### 问题2：Token验证失败
**症状**：登录成功但无法访问Metabase
**解决**：
1. 检查JWT密钥配置
2. 确认Token未过期
3. 验证Nginx auth_request配置

### 问题3：用户权限不正确
**症状**：用户登录成功但权限不符合预期
**解决**：
1. 检查权限映射表数据
2. 确认部门信息同步正确
3. 验证Metabase用户组配置

## 📊 监控检查清单

### 日常监控
- [ ] 认证成功率
- [ ] Token过期情况
- [ ] 用户同步状态
- [ ] 系统响应时间

### 定期检查
- [ ] 企业微信Token有效性
- [ ] 数据库连接状态
- [ ] Redis缓存状态
- [ ] Nginx配置正确性

## 🎯 下一步优化

### 功能增强
1. **单点登出**：实现统一登出功能
2. **权限细化**：支持更细粒度的权限控制
3. **用户同步**：定时同步企业微信组织架构
4. **审计日志**：记录用户操作日志

### 性能优化
1. **缓存优化**：优化Token和用户信息缓存策略
2. **连接池**：优化HTTP连接池配置
3. **负载均衡**：支持多实例部署

### 安全加固
1. **HTTPS**：启用SSL证书
2. **IP白名单**：限制访问来源
3. **频率限制**：防止暴力破解
4. **日志审计**：完善安全日志

---

*按照此指南，你应该能在4-6小时内完成基础集成。如有问题，请参考详细方案文档或联系技术支持。*
