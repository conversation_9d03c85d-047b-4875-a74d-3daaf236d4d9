# Metabase企业微信集成快速实施指南

## 🚀 快速开始

### 前置条件检查
- [ ] Metabase已部署并正常运行
- [ ] 企业微信管理员权限
- [ ] Java开发环境（JDK 8+）
- [ ] Redis服务
- [ ] MySQL数据库

## 📋 实施步骤

### 第一步：企业微信配置（30分钟）

#### 1.1 创建企业应用
1. 登录企业微信管理后台
2. 进入"应用管理" → "自建应用"
3. 创建新应用，记录以下信息：
   ```
   CorpID: ww1234567890abcdef
   AgentID: 1000001
   Secret: your_app_secret
   ```

#### 1.2 配置可信域名
1. 在应用详情页面设置"可信域名"
2. 添加你的域名（如：your-domain.com）
3. 下载验证文件并上传到网站根目录

#### 1.3 设置授权回调域
```
授权回调域: your-domain.com
授权回调URL: https://your-domain.com/auth/wechat/callback
```

### 第二步：数据库准备（15分钟）

#### 2.1 创建数据库表
```sql
-- 用户映射表
CREATE TABLE wechat_metabase_user_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    wechat_userid VARCHAR(64) NOT NULL UNIQUE,
    wechat_name VARCHAR(100),
    wechat_email VARCHAR(100),
    wechat_department VARCHAR(200),
    metabase_user_id INT,
    metabase_email VARCHAR(100),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 权限映射表
CREATE TABLE wechat_metabase_permission_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    wechat_department VARCHAR(200),
    metabase_group_id INT,
    metabase_group_name VARCHAR(100),
    permission_level ENUM('viewer', 'editor', 'admin') DEFAULT 'viewer',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认权限映射
INSERT INTO wechat_metabase_permission_mapping 
(wechat_department, metabase_group_id, metabase_group_name, permission_level) 
VALUES 
('技术部', 1, 'Administrators', 'admin'),
('产品部', 2, 'All Users', 'editor'),
('运营部', 2, 'All Users', 'viewer');
```

### 第三步：认证服务开发（2小时）

#### 3.1 创建Spring Boot项目
```bash
# 使用现有项目结构，在 jh-loan-cash-manage 中添加模块
mkdir -p cash-manage-system/src/main/java/com/jinghang/cash/modules/wechat
```

#### 3.2 添加依赖（pom.xml）
```xml
<!-- JWT -->
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt</artifactId>
    <version>0.9.1</version>
</dependency>

<!-- HTTP客户端 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- Redis -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

#### 3.3 核心代码文件清单
```
cash-manage-system/src/main/java/com/jinghang/cash/modules/wechat/
├── config/
│   ├── WechatWorkConfig.java          # 企业微信配置
│   └── RestTemplateConfig.java        # HTTP客户端配置
├── controller/
│   └── WechatAuthController.java      # 认证控制器
├── service/
│   ├── WechatWorkApiService.java      # 企业微信API服务
│   ├── MetabaseUserService.java       # Metabase用户管理
│   └── JwtTokenService.java           # JWT令牌服务
├── entity/
│   ├── WechatMetabaseUserMapping.java # 用户映射实体
│   └── WechatMetabasePermissionMapping.java # 权限映射实体
├── repository/
│   ├── WechatMetabaseUserMappingRepository.java
│   └── WechatMetabasePermissionMappingRepository.java
└── dto/
    ├── WechatWorkUser.java            # 企业微信用户DTO
    └── MetabaseUser.java              # Metabase用户DTO
```

### 第四步：Nginx配置（30分钟）

#### 4.1 安装Nginx（如未安装）
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 4.2 配置文件
创建 `/etc/nginx/sites-available/metabase-wechat`：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 企业微信验证文件
    location ~ ^/[A-Za-z0-9]{16}\.txt$ {
        root /var/www/html;
    }
    
    # 认证服务
    location /auth/ {
        proxy_pass http://localhost:9006/admin/api/wechat/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Metabase代理
    location / {
        # 检查认证状态
        auth_request /auth/verify;
        
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 传递用户信息到Metabase
        auth_request_set $user_email $upstream_http_x_user_email;
        proxy_set_header X-User-Email $user_email;
    }
    
    # 认证验证端点（内部使用）
    location = /auth/verify {
        internal;
        proxy_pass http://localhost:9006/admin/api/wechat/verify;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_header X-Original-Method $request_method;
    }
    
    # 登录页面不需要认证
    location /auth/login {
        proxy_pass http://localhost:9006/admin/api/wechat/login;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 回调页面不需要认证
    location /auth/callback {
        proxy_pass http://localhost:9006/admin/api/wechat/callback;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 4.3 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/metabase-wechat /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 第五步：配置文件设置（15分钟）

#### 5.1 Apollo配置（推荐）
在Apollo配置中心添加：
```properties
# 企业微信配置
wechat.work.corp-id=ww1234567890abcdef
wechat.work.corp-secret=your_corp_secret
wechat.work.agent-id=1000001
wechat.work.redirect-uri=https://your-domain.com/auth/callback

# JWT配置
jwt.secret=your_very_long_secret_key_here
jwt.expiration=7200

# Metabase配置
metabase.base-url=http://localhost:3000
metabase.admin-email=<EMAIL>
metabase.admin-password=your_admin_password
```

#### 5.2 本地配置（开发环境）
在 `application-dev.yml` 中添加：
```yaml
wechat:
  work:
    corp-id: ww1234567890abcdef
    corp-secret: your_corp_secret
    agent-id: 1000001
    redirect-uri: http://localhost/auth/callback

jwt:
  secret: your_jwt_secret_key
  expiration: 7200

metabase:
  base-url: http://localhost:3000
  admin-email: <EMAIL>
  admin-password: admin_password
```

### 第六步：测试验证（30分钟）

#### 6.1 启动服务
```bash
# 启动认证服务
cd jh-loan-cash-manage
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 启动Metabase（如果未启动）
java -jar metabase.jar
```

#### 6.2 测试流程
1. **访问登录页面**：`http://your-domain.com/auth/login`
2. **企业微信扫码登录**
3. **验证重定向到Metabase**
4. **检查用户权限**

#### 6.3 调试工具
```bash
# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 查看应用日志
tail -f logs/cash-manage.log

# 检查Redis缓存
redis-cli
> keys wechat_work_*
> get wechat_work_access_token
```

## 🔧 常见问题解决

### 问题1：企业微信回调失败
**症状**：点击登录后无响应或报错
**解决**：
1. 检查可信域名配置
2. 确认回调URL正确
3. 验证企业微信应用配置

### 问题2：Token验证失败
**症状**：登录成功但无法访问Metabase
**解决**：
1. 检查JWT密钥配置
2. 确认Token未过期
3. 验证Nginx auth_request配置

### 问题3：用户权限不正确
**症状**：用户登录成功但权限不符合预期
**解决**：
1. 检查权限映射表数据
2. 确认部门信息同步正确
3. 验证Metabase用户组配置

## 📊 监控检查清单

### 日常监控
- [ ] 认证成功率
- [ ] Token过期情况
- [ ] 用户同步状态
- [ ] 系统响应时间

### 定期检查
- [ ] 企业微信Token有效性
- [ ] 数据库连接状态
- [ ] Redis缓存状态
- [ ] Nginx配置正确性

## 🎯 下一步优化

### 功能增强
1. **单点登出**：实现统一登出功能
2. **权限细化**：支持更细粒度的权限控制
3. **用户同步**：定时同步企业微信组织架构
4. **审计日志**：记录用户操作日志

### 性能优化
1. **缓存优化**：优化Token和用户信息缓存策略
2. **连接池**：优化HTTP连接池配置
3. **负载均衡**：支持多实例部署

### 安全加固
1. **HTTPS**：启用SSL证书
2. **IP白名单**：限制访问来源
3. **频率限制**：防止暴力破解
4. **日志审计**：完善安全日志

---

*按照此指南，你应该能在4-6小时内完成基础集成。如有问题，请参考详细方案文档或联系技术支持。*
